# EdgeOne部署忽略文件

# 开发和测试文件
test-*.html
*-demo.html
FIXES.md
FIX-SUMMARY.md
现状
需求.txt

# 123云盘开放平台文档（仅开发时参考）
123云盘开放平台/

# 环境变量文件
.env
.env.local
.env.development
.env.production
.env.example
.env.edgeone.example

# Vercel特定文件（EdgeOne部署时不需要）
vercel.json
.vercel/
DEPLOYMENT.md

# 编辑器和系统文件
.DS_Store
Thumbs.db
*.swp
*.swo
*~
.vscode/
.idea/

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 依赖目录
node_modules/
.npm
.yarn
.pnpm-store/

# 临时文件
.tmp/
.temp/
.cache/

# 备份文件
*.bak
*.backup

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git相关（如果不需要）
.git/
.gitignore

# 构建输出（如果有）
dist/
build/
out/

# 测试覆盖率报告
coverage/
.nyc_output/

# 文档生成文件
docs/build/

# 本地配置文件
.local
*.local

# 调试文件
debug.log
error.log
