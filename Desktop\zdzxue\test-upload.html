<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>123云盘图床上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .upload-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .upload-btn:hover {
            background: #0056b3;
        }
        .upload-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .progress-container {
            margin: 20px 0;
            display: none;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #666;
        }
        .result-container {
            margin-top: 20px;
        }
        .result-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .result-item.success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .result-item.error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .result-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .config-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .config-info h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.success {
            background-color: #28a745;
        }
        .status-indicator.error {
            background-color: #dc3545;
        }
        .status-indicator.warning {
            background-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>123云盘图床上传测试</h1>
        
        <!-- 配置状态信息 -->
        <div class="config-info">
            <h3>配置状态</h3>
            <div id="configStatus">
                <div><span class="status-indicator warning"></span>正在检查配置...</div>
            </div>
        </div>

        <!-- 上传区域 -->
        <div class="upload-area" id="uploadArea">
            <p>点击选择图片或拖拽图片到此处</p>
            <p style="color: #666; font-size: 14px;">支持 JPG、PNG、GIF、WebP 格式，最大 10MB</p>
            <input type="file" id="fileInput" accept="image/*" multiple style="display: none;">
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">选择图片</button>
        </div>

        <!-- 进度条 -->
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备上传...</div>
        </div>

        <!-- 结果显示 -->
        <div class="result-container" id="resultContainer"></div>
    </div>

    <script>
        // 123云盘API配置
        window.CLOUD123_PARENT_FILE_ID = ''; // 测试时可以为空，使用本地预览模式
        
        // 图片上传器实例
        let imageUploader;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 动态加载图片上传器
            const script = document.createElement('script');
            script.src = 'school-review/js/image-upload.js';
            script.onload = function() {
                imageUploader = new ImageUploader();
                checkConfiguration();
            };
            document.head.appendChild(script);
            
            // 设置事件监听器
            setupEventListeners();
        });
        
        // 检查配置状态
        async function checkConfiguration() {
            const statusDiv = document.getElementById('configStatus');
            
            try {
                // 检查是否能获取access_token
                await imageUploader.getAccessToken();
                statusDiv.innerHTML = `
                    <div><span class="status-indicator success"></span>123云盘API配置正常</div>
                    <div><span class="status-indicator success"></span>access_token获取成功</div>
                    <div><span class="status-indicator ${window.CLOUD123_PARENT_FILE_ID ? 'success' : 'warning'}"></span>图床目录ID: ${window.CLOUD123_PARENT_FILE_ID || '未配置（将使用本地预览模式）'}</div>
                `;
            } catch (error) {
                statusDiv.innerHTML = `
                    <div><span class="status-indicator error"></span>123云盘API配置异常: ${error.message}</div>
                    <div><span class="status-indicator warning"></span>将使用本地预览模式</div>
                `;
            }
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            
            // 文件选择事件
            fileInput.addEventListener('change', handleFileSelect);
            
            // 拖拽事件
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFiles(files);
                }
            });
        }
        
        // 处理文件选择
        function handleFileSelect(e) {
            const files = e.target.files;
            if (files.length > 0) {
                handleFiles(files);
            }
        }
        
        // 处理文件上传
        async function handleFiles(files) {
            if (!imageUploader) {
                alert('图片上传器未初始化，请刷新页面重试');
                return;
            }
            
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const resultContainer = document.getElementById('resultContainer');
            
            // 显示进度条
            progressContainer.style.display = 'block';
            
            // 清空之前的结果
            resultContainer.innerHTML = '';
            
            try {
                // 批量上传文件
                const results = await imageUploader.uploadFiles(
                    files,
                    // 进度回调
                    function(progress, status) {
                        progressFill.style.width = progress + '%';
                        progressText.textContent = status;
                    },
                    // 单个文件完成回调
                    function(result, index, total) {
                        displayResult(result, index, total);
                    }
                );
                
                // 上传完成
                progressText.textContent = '上传完成！';
                
            } catch (error) {
                progressText.textContent = '上传失败: ' + error.message;
                displayResult({
                    success: false,
                    error: error.message,
                    filename: '批量上传'
                }, 1, 1);
            }
        }
        
        // 显示上传结果
        function displayResult(result, index, total) {
            const resultContainer = document.getElementById('resultContainer');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${result.success ? 'success' : 'error'}`;
            
            let html = `
                <h4>文件 ${index}/${total}: ${result.filename}</h4>
            `;
            
            if (result.success) {
                html += `
                    <p><strong>状态:</strong> 上传成功</p>
                    <p><strong>文件ID:</strong> ${result.fileID}</p>
                    <p><strong>访问URL:</strong> <a href="${result.url}" target="_blank">${result.url}</a></p>
                    ${result.isLocal ? '<p style="color: #ffc107;"><strong>注意:</strong> 这是本地预览模式，图片未实际上传到云端</p>' : ''}
                    <img src="${result.url}" alt="${result.filename}" class="result-image" onerror="this.style.display='none'">
                `;
            } else {
                html += `
                    <p><strong>状态:</strong> 上传失败</p>
                    <p><strong>错误:</strong> ${result.error}</p>
                `;
            }
            
            resultItem.innerHTML = html;
            resultContainer.appendChild(resultItem);
        }
    </script>
</body>
</html>
