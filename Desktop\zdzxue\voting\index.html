<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投票 - 重庆市梁平区知德中学</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/voting.css">
    <!-- 添加字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <header>
        <nav class="navbar">
            <div class="logo-container">
                <img src="../images/logo.jpg" alt="知德中学校徽" class="logo">
                <h1 class="school-name">重庆市梁平区知德中学</h1>
            </div>
            <div class="nav-links">
                <a href="../index.html" class="nav-link">首页</a>
                <!-- 管理员按钮，只有管理员才显示 -->
                <a href="../admin/index.html" class="nav-link admin-only" id="adminBtn" style="display: none;">后台管理</a>
                <a href="../school-review/index.html" class="nav-link">校评</a>
                <a href="../voting/index.html" class="nav-link active">投票</a>
            </div>
            <div class="user-actions">
                <div class="user-avatar" id="avatarContainer" style="display: none;">
                    <img src="../images/avatar.png" alt="用户头像" id="userAvatar">
                </div>
                <button class="login-btn" id="loginBtn">登录</button>
            </div>
        </nav>
    </header>

    <!-- 主要内容区域 -->
    <main class="voting-main">
        <!-- 页面标题区域 -->
        <section class="page-header">
            <div class="container">
                <h1 class="page-title">
                    <i class="fas fa-vote-yea"></i>
                    校园投票
                </h1>
                <p class="page-subtitle">参与校园民主决策，让你的声音被听见</p>
            </div>
        </section>

        <!-- 投票内容区域 -->
        <section class="voting-content">
            <div class="container">
                <!-- 投票统计信息 -->
                <div class="voting-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="totalVotes">0</div>
                        <div class="stat-label">总投票数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="activeVotes">0</div>
                        <div class="stat-label">进行中</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="myVotes">0</div>
                        <div class="stat-label">我发起的</div>
                    </div>
                </div>

                <!-- 投票筛选和排序 -->
                <div class="voting-controls">
                    <div class="filter-section">
                        <button class="filter-btn active" data-filter="all">全部投票</button>
                        <button class="filter-btn" data-filter="active">进行中</button>
                        <button class="filter-btn" data-filter="ended">已结束</button>
                        <button class="filter-btn" data-filter="my">我发起的</button>
                    </div>
                    <div class="sort-section">
                        <select class="sort-select" id="sortSelect">
                            <option value="time-desc">最新发布</option>
                            <option value="time-asc">最早发布</option>
                            <option value="votes-desc">参与人数最多</option>
                            <option value="votes-asc">参与人数最少</option>
                        </select>
                    </div>
                </div>

                <!-- 投票列表 -->
                <div class="voting-list" id="votingList">
                    <!-- 投票项目将通过JavaScript动态生成 -->
                </div>

                <!-- 分页控制 -->
                <div class="pagination-container" id="paginationContainer">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </div>
            </div>
        </section>
    </main>

    <!-- 新建投票悬浮按钮 -->
    <div class="create-vote-fab" id="createVoteFab">
        <i class="fas fa-plus"></i>
        <span class="fab-tooltip">新建投票</span>
    </div>

    <!-- 新建投票弹窗 -->
    <div class="modal-overlay" id="createVoteModal">
        <div class="vote-modal">
            <div class="modal-header">
                <h3 class="modal-title">新建投票</h3>
                <button class="close-modal" id="closeCreateModal">&times;</button>
            </div>

            <div class="modal-content">
                <div class="notification" id="createVoteNotification"></div>

                <!-- 投票基本信息 -->
                <div class="form-group">
                    <label for="voteTitle">投票标题 *</label>
                    <input type="text" class="form-control" id="voteTitle" placeholder="请输入投票标题" maxlength="100">
                    <div class="char-count">
                        <span id="titleCharCount">0</span>/100
                    </div>
                </div>

                <div class="form-group">
                    <label for="voteDescription">投票描述（可选）</label>
                    <textarea class="form-control" id="voteDescription" placeholder="请输入投票描述" rows="3" maxlength="500"></textarea>
                    <div class="char-count">
                        <span id="descCharCount">0</span>/500
                    </div>
                </div>

                <!-- 投票选项 -->
                <div class="form-group">
                    <label>投票选项 *</label>
                    <div class="vote-options" id="voteOptions">
                        <div class="option-item">
                            <input type="text" class="option-input" placeholder="选项一" maxlength="50">
                            <button class="remove-option" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="option-item">
                            <input type="text" class="option-input" placeholder="选项二" maxlength="50">
                            <button class="remove-option" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <button class="add-option-btn" id="addOptionBtn">
                        <i class="fas fa-plus"></i>
                        添加选项
                    </button>
                </div>

                <!-- 截止时间设置 -->
                <div class="form-group">
                    <div class="deadline-toggle">
                        <label class="toggle-label">
                            <input type="checkbox" id="enableDeadline">
                            <span class="toggle-slider"></span>
                            <span>设置截止时间</span>
                        </label>
                    </div>
                    <div class="deadline-settings" id="deadlineSettings" style="display: none;">
                        <div class="datetime-group">
                            <div class="date-input">
                                <label for="deadlineDate">截止日期</label>
                                <input type="date" class="form-control" id="deadlineDate">
                            </div>
                            <div class="time-input">
                                <label for="deadlineTime">截止时间</label>
                                <input type="time" class="form-control" id="deadlineTime">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 投票设置 -->
                <div class="form-group">
                    <label>投票设置</label>
                    <div class="vote-settings">
                        <label class="setting-item">
                            <input type="checkbox" id="allowMultiple">
                            <span class="checkmark"></span>
                            <span>允许多选</span>
                        </label>
                        <label class="setting-item">
                            <input type="checkbox" id="showResults" checked>
                            <span class="checkmark"></span>
                            <span>实时显示结果</span>
                        </label>
                        <label class="setting-item">
                            <input type="checkbox" id="anonymousVote">
                            <span class="checkmark"></span>
                            <span>匿名投票</span>
                        </label>
                    </div>
                </div>

                <!-- 按钮组 -->
                <div class="modal-buttons">
                    <button class="cancel-btn" id="cancelCreateBtn">取消</button>
                    <button class="submit-btn" id="createVoteBtn">创建投票</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 投票详情弹窗 -->
    <div class="modal-overlay" id="voteDetailModal">
        <div class="vote-detail-modal">
            <div class="modal-header">
                <h3 class="modal-title" id="voteDetailTitle">投票详情</h3>
                <button class="close-modal" id="closeDetailModal">&times;</button>
            </div>

            <div class="modal-content" id="voteDetailContent">
                <!-- 投票详情内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div class="modal-overlay" id="deleteConfirmModal">
        <div class="confirm-modal">
            <div class="modal-header">
                <h3 class="modal-title">确认删除</h3>
                <button class="close-modal" id="closeDeleteModal">&times;</button>
            </div>

            <div class="modal-content">
                <div class="confirm-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>您确定要删除这个投票吗？</p>
                    <p class="warning-text">此操作不可撤销，所有投票数据将被永久删除。</p>
                </div>

                <div class="modal-buttons">
                    <button class="cancel-btn" id="cancelDeleteBtn">取消</button>
                    <button class="delete-btn" id="confirmDeleteBtn">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录弹窗（复用主页的登录弹窗） -->
    <div class="modal-overlay" id="loginModal">
        <div class="login-modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">用户登录</h3>
                <button class="close-modal" id="closeModal">&times;</button>
            </div>

            <div class="modal-tabs">
                <button class="modal-tab active" id="loginTab">登录</button>
                <button class="modal-tab" id="registerTab">注册</button>
            </div>

            <!-- 登录表单 -->
            <div class="form-tab active" id="loginForm">
                <div class="notification" id="loginNotification"></div>

                <div class="form-group">
                    <label for="loginUsername">用户名</label>
                    <input type="text" class="form-control" id="loginUsername" placeholder="请输入用户名">
                </div>

                <div class="form-group">
                    <label for="loginPassword">密码</label>
                    <input type="password" class="form-control" id="loginPassword" placeholder="请输入密码">
                </div>

                <div class="form-footer">
                    <div class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <label for="rememberMe">记住我</label>
                    </div>
                    <a href="#" class="forgot-password">忘记密码?</a>
                </div>

                <button class="submit-btn" id="loginButton">登录</button>

                <div class="register-prompt">
                    还没有账号? <a href="#" class="register-link" id="switchToRegister">立即注册</a>
                </div>
            </div>

            <!-- 注册表单 -->
            <div class="form-tab" id="registerForm">
                <div class="notification" id="registerNotification"></div>

                <div class="form-group">
                    <label for="registerUsername">用户名</label>
                    <input type="text" class="form-control" id="registerUsername" placeholder="请设置用户名">
                </div>

                <div class="form-group">
                    <label for="registerPassword">密码</label>
                    <input type="password" class="form-control" id="registerPassword" placeholder="请设置密码">
                </div>

                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input type="password" class="form-control" id="confirmPassword" placeholder="请再次输入密码">
                </div>

                <button class="submit-btn" id="registerButton">注册</button>

                <div class="register-prompt">
                    已有账号? <a href="#" class="register-link" id="switchToLogin">立即登录</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 个人信息弹窗（复用主页的个人信息弹窗） -->
    <div class="modal-overlay" id="profileModal">
        <div class="profile-modal">
            <div class="modal-header">
                <h3 class="modal-title">个人信息</h3>
                <button class="close-modal" id="closeProfileModal">&times;</button>
            </div>

            <div class="profile-content">
                <div class="notification" id="profileNotification"></div>

                <div class="avatar-section">
                    <div class="current-avatar">
                        <img src="../images/avatar.png" alt="当前头像" id="currentAvatar">
                    </div>
                    <div class="avatar-upload">
                        <label for="avatarUpload" class="avatar-upload-label">更换头像</label>
                        <input type="file" id="avatarUpload" accept="image/*" style="display: none;">
                    </div>
                </div>

                <div class="form-group">
                    <label for="profileUsername">用户名</label>
                    <input type="text" class="form-control" id="profileUsername" placeholder="请输入新用户名">
                </div>

                <div class="user-id-section">
                    <p>用户ID: <span id="userUniqueId">UID_XXXXX</span></p>
                    <p class="id-note">（此ID为您的唯一身份标识，无法修改）</p>
                </div>

                <div class="profile-buttons">
                    <button class="submit-btn" id="saveProfileButton">保存修改</button>
                    <button class="logout-btn" id="logoutButton">退出登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="../js/main.js"></script>
    <script src="js/vote-system.js"></script>
    <script src="js/voting.js"></script>
</body>
</html>
