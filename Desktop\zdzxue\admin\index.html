<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理 - 重庆市梁平区知德中学</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <!-- 添加字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <header>
        <nav class="navbar">
            <div class="logo-container">
                <img src="../images/logo.jpg" alt="知德中学校徽" class="logo">
                <h1 class="school-name">重庆市梁平区知德中学</h1>
            </div>
            <div class="nav-links">
                <a href="../index.html" class="nav-link">首页</a>
                <!-- 管理员按钮，只有管理员才显示 -->
                <a href="../admin/index.html" class="nav-link admin-only active" id="adminBtn">后台管理</a>
                <a href="../school-review/index.html" class="nav-link">校评</a>
                <a href="../voting/index.html" class="nav-link">投票</a>
            </div>
            <div class="user-actions">
                <div class="user-avatar" id="avatarContainer" style="display: none;">
                    <img src="../images/avatar.png" alt="用户头像" id="userAvatar">
                </div>
                <button class="login-btn" id="loginBtn">登录</button>
            </div>
        </nav>
    </header>

    <!-- 主要内容区域 -->
    <main class="admin-main">
        <!-- 页面标题 -->
        <div class="admin-header">
            <div class="container">
                <h1 class="admin-title">
                    <i class="fas fa-cogs"></i>
                    后台管理系统
                </h1>
                <p class="admin-subtitle">用户管理 · 头衔设置 · 权限控制</p>
            </div>
        </div>

        <!-- 管理面板 -->
        <div class="admin-content">
            <div class="container">
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalUsers">0</h3>
                            <p>总用户数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="titledUsers">0</h3>
                            <p>有头衔用户</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-ban"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="bannedUsers">0</h3>
                            <p>被禁言用户</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="adminUsers">0</h3>
                            <p>管理员数量</p>
                        </div>
                    </div>
                </div>

                <!-- 用户管理区域 -->
                <div class="management-section">
                    <div class="section-header">
                        <h2>
                            <i class="fas fa-users-cog"></i>
                            用户管理
                        </h2>
                        <div class="search-container">
                            <input type="text" id="userSearch" placeholder="搜索用户名..." class="search-input">
                            <button id="searchBtn" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 用户列表 -->
                    <div class="user-list-container">
                        <div class="user-list-header">
                            <div class="header-cell">用户信息</div>
                            <div class="header-cell">角色</div>
                            <div class="header-cell">头衔</div>
                            <div class="header-cell">状态</div>
                            <div class="header-cell">操作</div>
                        </div>
                        <div id="userList" class="user-list">
                            <!-- 用户列表将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 分页控制 -->
                    <div class="pagination-container">
                        <button id="prevPage" class="pagination-btn" disabled>
                            <i class="fas fa-chevron-left"></i>
                            上一页
                        </button>
                        <span id="pageInfo" class="page-info">第 1 页，共 1 页</span>
                        <button id="nextPage" class="pagination-btn" disabled>
                            下一页
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 头衔设置弹窗 -->
    <div id="titleModal" class="modal-overlay">
        <div class="modal-content title-modal">
            <div class="modal-header">
                <h3>
                    <i class="fas fa-crown"></i>
                    设置用户头衔
                </h3>
                <button id="closeTitleModal" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="targetUsername">目标用户</label>
                    <input type="text" id="targetUsername" readonly class="form-input">
                </div>
                <div class="form-group">
                    <label for="titleText">头衔名称</label>
                    <input type="text" id="titleText" placeholder="请输入头衔名称" class="form-input" maxlength="10">
                    <small class="form-hint">最多10个字符</small>
                </div>
                <div class="form-group">
                    <label for="titleColor">头衔颜色</label>
                    <div class="color-picker-container">
                        <input type="color" id="titleColor" value="#6c5ce7" class="color-picker">
                        <div class="color-presets">
                            <button class="color-preset" data-color="#6c5ce7" style="background: #6c5ce7;"></button>
                            <button class="color-preset" data-color="#e74c3c" style="background: #e74c3c;"></button>
                            <button class="color-preset" data-color="#f39c12" style="background: #f39c12;"></button>
                            <button class="color-preset" data-color="#27ae60" style="background: #27ae60;"></button>
                            <button class="color-preset" data-color="#3498db" style="background: #3498db;"></button>
                            <button class="color-preset" data-color="#9b59b6" style="background: #9b59b6;"></button>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label>头衔预览</label>
                    <div class="title-preview">
                        <span id="previewUsername">用户名</span>
                        <span id="previewTitle" class="user-title" style="background: #6c5ce7;">示例头衔</span>
                    </div>
                </div>
                <div class="notification" id="titleNotification"></div>
            </div>
            <div class="modal-footer">
                <button id="removeTitleBtn" class="btn btn-danger">
                    <i class="fas fa-trash"></i>
                    移除头衔
                </button>
                <button id="saveTitleBtn" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    保存头衔
                </button>
            </div>
        </div>
    </div>

    <!-- 禁言设置弹窗 -->
    <div id="banModal" class="modal-overlay">
        <div class="modal-content ban-modal">
            <div class="modal-header">
                <h3>
                    <i class="fas fa-ban"></i>
                    禁言管理
                </h3>
                <button id="closeBanModal" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="banUsername">目标用户</label>
                    <input type="text" id="banUsername" readonly class="form-input">
                </div>
                <div class="form-group">
                    <label for="banReason">禁言原因</label>
                    <textarea id="banReason" placeholder="请输入禁言原因..." class="form-textarea" maxlength="200"></textarea>
                    <small class="form-hint">最多200个字符</small>
                </div>
                <div class="notification" id="banNotification"></div>
            </div>
            <div class="modal-footer">
                <button id="unbanBtn" class="btn btn-success">
                    <i class="fas fa-check"></i>
                    解除禁言
                </button>
                <button id="confirmBanBtn" class="btn btn-danger">
                    <i class="fas fa-ban"></i>
                    确认禁言
                </button>
            </div>
        </div>
    </div>

    <!-- 个人信息弹窗 -->
    <div class="modal-overlay" id="profileModal">
        <div class="profile-modal">
            <div class="modal-header">
                <h3 class="modal-title">个人信息</h3>
                <button class="close-modal" id="closeProfileModal">&times;</button>
            </div>

            <div class="profile-content">
                <div class="notification" id="profileNotification"></div>

                <div class="avatar-section">
                    <div class="current-avatar">
                        <img src="../images/avatar.png" alt="当前头像" id="currentAvatar">
                    </div>
                    <div class="avatar-upload">
                        <label for="avatarUpload" class="avatar-upload-label">更换头像</label>
                        <input type="file" id="avatarUpload" accept="image/*" style="display: none;">
                    </div>
                </div>

                <div class="form-group">
                    <label for="profileUsername">用户名</label>
                    <input type="text" class="form-control" id="profileUsername" placeholder="请输入新用户名">
                </div>

                <div class="user-id-section">
                    <p>用户ID: <span id="userUniqueId">UID_XXXXX</span></p>
                    <p class="id-note">（此ID为您的唯一身份标识，无法修改）</p>
                </div>

                <div class="profile-buttons">
                    <button class="submit-btn" id="saveProfileButton">保存修改</button>
                    <button class="logout-btn" id="logoutButton">退出登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="../js/main.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
