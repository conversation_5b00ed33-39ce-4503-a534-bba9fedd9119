# EdgeOne部署环境变量配置示例
# 复制此文件为 .env.local 并填入实际值

# 123云盘图床配置
CLOUD123_ACCESS_TOKEN=your_123pan_access_token_here
CLOUD123_PARENT_FILE_ID=your_123pan_folder_id_here

# 数据库配置（可选，如果使用外部数据库）
DATABASE_URL=your_database_connection_string_here

# 环境标识
NODE_ENV=production

# EdgeOne特定配置
EDGEONE_PROJECT_ID=your_edgeone_project_id
EDGEONE_ZONE_ID=your_edgeone_zone_id

# 安全配置
JWT_SECRET=your_jwt_secret_key_here
ADMIN_PASSWORD_HASH=your_admin_password_hash_here

# 第三方服务配置（如果需要）
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password

# 监控和分析（可选）
ANALYTICS_ID=your_analytics_tracking_id
SENTRY_DSN=your_sentry_dsn_for_error_tracking

# 注意事项：
# 1. 请勿将此文件提交到版本控制系统
# 2. 在EdgeOne控制台的环境变量设置中配置这些值
# 3. 确保所有敏感信息都通过环境变量传递，不要硬编码在代码中
# 4. EdgeOne支持在不同环境（开发/测试/生产）中使用不同的环境变量值
