{"version": 2, "name": "zhide-school-website", "public": true, "github": {"silent": true}, "buildCommand": "", "outputDirectory": ".", "headers": [{"source": "/(.*)\\.css", "headers": [{"key": "Content-Type", "value": "text/css; charset=utf-8"}]}, {"source": "/(.*)\\.js", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}]}, {"source": "/(.*)\\.html", "headers": [{"key": "Content-Type", "value": "text/html; charset=utf-8"}]}, {"source": "/(.*)\\.png", "headers": [{"key": "Content-Type", "value": "image/png"}]}, {"source": "/(.*)\\.jpg", "headers": [{"key": "Content-Type", "value": "image/jpeg"}]}, {"source": "/(.*)\\.jpeg", "headers": [{"key": "Content-Type", "value": "image/jpeg"}]}], "rewrites": [{"source": "/", "destination": "/index.html"}, {"source": "/school-review", "destination": "/school-review/index.html"}, {"source": "/school-review/(.*)", "destination": "/school-review/$1"}, {"source": "/admin", "destination": "/admin/index.html"}, {"source": "/admin/(.*)", "destination": "/admin/$1"}, {"source": "/voting", "destination": "/voting/index.html"}, {"source": "/voting/(.*)", "destination": "/voting/$1"}, {"source": "/api/(.*)", "destination": "/api/$1"}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/review", "destination": "/school-review", "permanent": true}], "trailingSlash": false, "cleanUrls": true}