# 🚀 腾讯云EdgeOne部署指南

## 📋 部署前准备

### 1. 环境变量配置
在EdgeOne项目设置中添加以下环境变量：

```bash
# 123云盘图床配置
CLOUD123_ACCESS_TOKEN=your_access_token_here
CLOUD123_PARENT_FILE_ID=your_parent_file_id_here

# 数据库配置（可选）
DATABASE_URL=your_database_url_here

# 环境标识
NODE_ENV=production
```

### 2. 项目结构确认
确保项目包含以下核心文件：
- `index.html` - 主页
- `school-review/index.html` - 校评页面
- `admin/index.html` - 后台管理页面
- `voting/index.html` - 投票页面
- `edgeone.json` - EdgeOne配置文件

## 🔧 EdgeOne 配置说明

### 路由配置
- **主页**: `/` → `/index.html`
- **校评页面**: `/school-review` → `/school-review/index.html`
- **后台管理**: `/admin` → `/admin/index.html`
- **投票页面**: `/voting` → `/voting/index.html`

### 缓存策略
- **静态资源** (CSS/JS/图片/字体): 1年缓存，不可变
- **HTML文件**: 无缓存，每次重新验证
- **API响应**: 根据具体需求配置

### 安全头部
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: SAMEORIGIN`
- `X-XSS-Protection: 1; mode=block`
- 自动HTTPS重定向
- GZIP压缩

## 📦 部署步骤

### 方法一：GitHub 集成部署
1. 将代码推送到 GitHub 仓库
2. 在腾讯云EdgeOne控制台中创建新项目
3. 选择"从Git仓库导入"
4. 连接GitHub账号并选择仓库
5. 配置构建设置：
   - 构建命令：留空（静态网站）
   - 输出目录：`.`（根目录）
   - 安装命令：留空
6. 配置环境变量
7. 点击部署

### 方法二：本地上传部署
1. 将项目文件打包为ZIP格式
2. 在EdgeOne控制台选择"上传文件"
3. 上传ZIP文件
4. 配置环境变量
5. 点击部署

### 方法三：CLI部署（推荐）
```bash
# 安装EdgeOne CLI
npm install -g @tencent-cloud/edgeone-cli

# 登录EdgeOne
edgeone login

# 初始化项目
edgeone init

# 部署项目
edgeone deploy

# 生产环境部署
edgeone deploy --prod
```

## 🔍 部署验证

### 功能测试清单
- [ ] 主页正常加载
- [ ] 导航栏功能正常
- [ ] 校评页面可访问
- [ ] 后台管理页面可访问（需管理员权限）
- [ ] 投票页面可访问
- [ ] 登录功能正常
- [ ] 图片上传功能正常（需配置123云盘）
- [ ] 响应式设计在移动端正常
- [ ] HTTPS访问正常
- [ ] CDN加速生效

### 性能检查
- [ ] 页面加载速度 < 2秒（EdgeOne CDN加速）
- [ ] 静态资源正确缓存
- [ ] 图片优化加载
- [ ] CSS/JS 文件正确压缩
- [ ] GZIP压缩启用

## 🛠️ 常见问题解决

### 1. 页面404错误
**问题**: 访问子页面时出现404
**解决**: 
- 检查 `edgeone.json` 中的路由配置是否正确
- 确保所有HTML文件路径正确
- 检查大小写敏感性

### 2. 静态资源加载失败
**问题**: CSS/JS文件无法加载
**解决**: 
- 确保文件路径正确，使用相对路径
- 检查EdgeOne的静态资源配置
- 验证CDN缓存设置

### 3. 环境变量未生效
**问题**: 123云盘API调用失败
**解决**: 
- 检查EdgeOne项目设置中的环境变量配置
- 确保变量名称与代码中使用的一致
- 重新部署项目使环境变量生效
- 检查变量值是否包含特殊字符需要转义

### 4. HTTPS证书问题
**问题**: HTTPS访问异常
**解决**:
- 在EdgeOne控制台检查SSL证书状态
- 确保域名解析正确指向EdgeOne
- 等待证书自动签发完成（通常需要几分钟）

### 5. CDN缓存问题
**问题**: 更新后内容未生效
**解决**:
- 在EdgeOne控制台手动清除缓存
- 检查缓存策略配置
- 使用版本号或时间戳避免缓存问题

## 📊 监控和维护

### 性能监控
- 使用EdgeOne Analytics监控页面性能
- 定期检查CDN命中率
- 监控API调用成功率和响应时间
- 查看访问日志和错误日志

### 日志查看
```bash
# 查看部署日志
edgeone logs

# 查看实时日志
edgeone logs --follow

# 查看错误日志
edgeone logs --level error
```

### 更新部署
```bash
# 重新部署
edgeone deploy --prod

# 回滚到上一版本
edgeone rollback

# 查看部署历史
edgeone deployments
```

## 🌐 域名配置

### 自定义域名
1. 在EdgeOne控制台添加自定义域名
2. 配置DNS解析：
   ```
   类型: CNAME
   名称: www (或其他子域名)
   值: your-project.edgeone.app
   ```
3. 等待DNS生效（通常需要几分钟到几小时）
4. EdgeOne会自动为自定义域名签发SSL证书

### 域名重定向
```json
{
  "redirects": [
    {
      "source": "example.com",
      "destination": "https://www.example.com",
      "permanent": true
    }
  ]
}
```

## 🔒 安全配置

### WAF防护
- 在EdgeOne控制台启用Web应用防火墙
- 配置SQL注入防护
- 启用XSS攻击防护
- 设置CC攻击防护

### 访问控制
```json
{
  "headers": [
    {
      "source": "/admin/(.*)",
      "headers": [
        {
          "key": "X-Robots-Tag",
          "value": "noindex, nofollow"
        }
      ]
    }
  ]
}
```

## 💰 成本优化

### 流量优化
- 启用GZIP压缩减少传输大小
- 优化图片格式和大小
- 使用WebP格式图片
- 合理设置缓存策略

### 带宽节省
- 使用EdgeOne的图片优化功能
- 启用Brotli压缩
- 配置智能压缩

## 🔗 相关链接

- [腾讯云EdgeOne官方文档](https://cloud.tencent.com/document/product/1552)
- [EdgeOne CLI文档](https://cloud.tencent.com/document/product/1552/81902)
- [EdgeOne定价说明](https://cloud.tencent.com/document/product/1552/77380)
- [123云盘开放平台](https://www.123pan.com/developer)

## 📞 技术支持

如遇到部署问题，可通过以下方式获取帮助：
- 腾讯云工单系统
- EdgeOne技术交流群
- 官方技术文档
- 社区论坛

---

**注意**: EdgeOne是腾讯云的边缘计算平台，提供了比传统CDN更强大的边缘计算能力，特别适合需要低延迟和高性能的Web应用。
