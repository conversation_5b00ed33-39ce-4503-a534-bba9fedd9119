# 123云盘图床配置说明

## 概述

本项目已集成123云盘图床功能，用于校评页面的图片上传和存储。系统会自动处理图片上传、获取访问链接等操作。

## 配置步骤

### 1. 申请123云盘开发者账号

1. 访问 [123开放平台官网](https://www.123pan.com/developer)
2. 阅读开发者协议，填写必填信息
3. 申请 `client_id` 和 `client_secret`
4. 申请通过后会发送到您的邮箱，请妥善保存

### 2. 创建图床目录

1. 登录123云盘网页版
2. 在图床空间中创建一个专门的目录用于存储校评图片（如：`school-review-images`）
3. 记录该目录的ID（可通过API获取或浏览器开发者工具查看）

### 3. 配置Vercel环境变量

在Vercel项目设置中添加以下环境变量：

```
CLOUD123_CLIENT_ID=你的client_id
CLOUD123_CLIENT_SECRET=你的client_secret
CLOUD123_PARENT_FILE_ID=图床目录ID
```

### 4. 部署项目

配置完成后重新部署项目，图片上传功能即可正常使用。

## 功能特性

### 自动认证
- 系统自动获取和管理access_token
- 令牌过期前自动刷新
- 无需手动配置访问令牌

### 图片上传流程
1. **文件验证** - 检查文件类型和大小
2. **MD5计算** - 计算文件校验值
3. **创建文件** - 在123云盘创建文件记录
4. **秒传检测** - 如果文件已存在则秒传
5. **分片上传** - 大文件自动分片上传
6. **获取直链** - 自动获取图片访问URL

### 错误处理
- API调用失败时自动降级到本地预览模式
- 详细的错误日志记录
- 用户友好的错误提示

## API限制

根据123云盘开放平台限制：

- `api/v1/access_token`: 1次/秒
- `upload/v1/oss/file/create`: 2次/秒
- `upload/v1/oss/file/get_upload_url`: 无限制
- `upload/v1/oss/file/upload_complete`: 无限制

## 文件结构

```
api/
├── cloud123-auth.js          # 认证服务（Node.js环境）
└── cloud123-token.js         # Vercel API端点

school-review/
└── js/
    └── image-upload.js       # 图片上传类（浏览器环境）

.env.example                  # 环境变量示例
vercel.json                   # Vercel配置
```

## 故障排除

### 1. 图片上传失败
- 检查Vercel环境变量是否正确配置
- 查看浏览器控制台错误信息
- 确认123云盘账号状态正常

### 2. 获取access_token失败
- 验证client_id和client_secret是否正确
- 检查123云盘账号是否有效
- 确认API调用频率未超限

### 3. 图片无法显示
- 检查图床目录ID是否正确
- 确认直链功能已启用
- 验证图片文件是否上传成功

## 注意事项

1. **安全性**: client_secret仅在服务端使用，不会暴露给前端
2. **存储**: 图片存储在123云盘图床空间，不占用普通云盘容量
3. **流量**: 图片访问会消耗123云盘直链流量
4. **备份**: 建议定期备份重要图片数据

## 技术支持

如遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查Vercel函数日志
3. 参考123云盘开放平台文档
4. 联系技术支持

---

配置完成后，用户在校评页面上传图片时，系统会自动：
- 上传图片到123云盘图床
- 获取图片直链URL
- 在评论中正确显示图片
- 处理各种异常情况
