<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS加载测试</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .test-container {
            padding: 20px;
            margin: 20px;
            border: 2px solid #ccc;
            border-radius: 8px;
        }
        .test-success {
            color: green;
            font-weight: bold;
        }
        .test-fail {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CSS加载测试页面</h1>
        
        <div class="test-section">
            <h2>测试项目：</h2>
            
            <div class="test-item">
                <h3>1. 主CSS文件加载测试</h3>
                <div class="navbar" style="height: 60px; margin: 10px 0;">
                    <div class="logo-container">
                        <span>如果这个导航栏有紫色背景，说明CSS加载成功</span>
                    </div>
                </div>
                <p id="css-test-result">检测中...</p>
            </div>
            
            <div class="test-item">
                <h3>2. CSS变量测试</h3>
                <div style="background-color: var(--primary-color); color: white; padding: 10px; margin: 10px 0;">
                    如果这个区域有紫色背景，说明CSS变量正常工作
                </div>
            </div>
            
            <div class="test-item">
                <h3>3. 字体图标测试</h3>
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
                <i class="fas fa-home"></i> 如果左边有房子图标，说明字体图标加载成功
            </div>
        </div>
        
        <div class="test-actions">
            <button onclick="runTests()" style="padding: 10px 20px; background: var(--primary-color); color: white; border: none; border-radius: 5px; cursor: pointer;">
                运行测试
            </button>
        </div>
        
        <div id="test-results" style="margin-top: 20px;"></div>
    </div>

    <script>
        function runTests() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h3>测试结果：</h3>';
            
            // 测试1: 检查CSS是否加载
            const navbar = document.querySelector('.navbar');
            const navbarStyle = window.getComputedStyle(navbar);
            const backgroundColor = navbarStyle.backgroundColor;
            
            if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent') {
                results.innerHTML += '<p class="test-success">✅ 主CSS文件加载成功</p>';
                document.getElementById('css-test-result').innerHTML = '<span class="test-success">CSS加载成功</span>';
            } else {
                results.innerHTML += '<p class="test-fail">❌ 主CSS文件加载失败</p>';
                document.getElementById('css-test-result').innerHTML = '<span class="test-fail">CSS加载失败</span>';
            }
            
            // 测试2: 检查CSS变量
            const testDiv = document.createElement('div');
            testDiv.style.color = 'var(--primary-color)';
            document.body.appendChild(testDiv);
            const computedColor = window.getComputedStyle(testDiv).color;
            document.body.removeChild(testDiv);
            
            if (computedColor && computedColor !== 'rgb(0, 0, 0)') {
                results.innerHTML += '<p class="test-success">✅ CSS变量工作正常</p>';
            } else {
                results.innerHTML += '<p class="test-fail">❌ CSS变量不工作</p>';
            }
            
            // 测试3: 检查当前页面路径
            results.innerHTML += `<p><strong>当前页面路径:</strong> ${window.location.pathname}</p>`;
            results.innerHTML += `<p><strong>CSS文件路径:</strong> css/style.css</p>`;
            
            // 测试4: 尝试加载CSS文件
            fetch('css/style.css')
                .then(response => {
                    if (response.ok) {
                        results.innerHTML += '<p class="test-success">✅ CSS文件可以通过fetch访问</p>';
                    } else {
                        results.innerHTML += '<p class="test-fail">❌ CSS文件无法通过fetch访问 (状态码: ' + response.status + ')</p>';
                    }
                })
                .catch(error => {
                    results.innerHTML += '<p class="test-fail">❌ CSS文件fetch失败: ' + error.message + '</p>';
                });
        }
        
        // 页面加载完成后自动运行测试
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
